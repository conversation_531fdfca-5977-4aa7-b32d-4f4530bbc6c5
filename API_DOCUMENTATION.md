# 旁白统计接口文档

## 接口概述

新增了一个用于计算会话下所有章节分镜旁白数量的接口，该接口会解析分镜数据中的`shot_data`字段，统计其中`line_list`里面`name`为"旁白"的条目数量，并将结果缓存到Redis中。

## API 端点

### 计算会话旁白数量

**请求方式:** `GET`

**请求路径:** `/agent/ai-shot/narration-count/{sessionId}`

**路径参数:**
- `sessionId` (String, 必填): 会话ID

**响应格式:**

```json
{
  "success": true,
  "data": {
    "sessionId": "session-123",
    "totalNarrationCount": 5,
    "chapters": [
      {
        "segmentId": "segment-1",
        "segmentName": "第一章",
        "narrationCount": 3,
        "narrationDetails": [
          {
            "shotId": "C01-SC01-25",
            "narrationId": 1,
            "narrationLine": "希望这些建议能帮助你更好地面对经济萧条。我们一起加油！"
          },
          {
            "shotId": "C01-SC01-26",
            "narrationId": 1,
            "narrationLine": "接下来我们来看看具体的应对策略。"
          },
          {
            "shotId": "C01-SC01-27",
            "narrationId": 2,
            "narrationLine": "这是另一段旁白内容。"
          }
        ]
      },
      {
        "segmentId": "segment-2",
        "segmentName": "第二章",
        "narrationCount": 2,
        "narrationDetails": [
          {
            "shotId": "C02-SC01-01",
            "narrationId": 1,
            "narrationLine": "第二章的旁白内容。"
          },
          {
            "shotId": "C02-SC01-02",
            "narrationId": 1,
            "narrationLine": "继续第二章的内容。"
          }
        ]
      }
    ]
  }
}
```

**响应字段说明:**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| sessionId | String | 会话ID |
| totalNarrationCount | Integer | 总旁白数量 |
| chapters | Array | 章节旁白统计列表 |
| chapters[].segmentId | String | 章节ID |
| chapters[].segmentName | String | 章节名称 |
| chapters[].narrationCount | Integer | 该章节的旁白数量 |
| chapters[].narrationDetails | Array | 旁白详情列表 |
| narrationDetails[].shotId | String | 分镜ID |
| narrationDetails[].narrationId | Integer | 旁白ID（来自line_list中的id字段） |
| narrationDetails[].narrationLine | String | 旁白内容（来自line_list中的line字段） |

## 功能特性

### 1. 数据解析逻辑
- 根据`sessionId`查询`ai_shot`表获取所有分镜数据
- 解析每个分镜的`shot_data` JSON字段
- 遍历`shot_data.line_list`数组，统计`name`字段值为"旁白"的条目
- 记录旁白的分镜ID、旁白ID和旁白内容

### 2. Redis缓存机制
- **缓存Key格式:** `NARRATION_COUNT:SESSION:{sessionId}`
- **缓存时间:** 24小时
- **缓存策略:** 优先从缓存获取，缓存不存在时查询数据库并更新缓存

### 3. 错误处理
- 参数校验：会话ID不能为空
- JSON解析异常：单个分镜解析失败不影响整体流程
- Redis异常：缓存操作失败不影响主流程

## 使用示例

### cURL 请求示例

```bash
curl -X GET "http://localhost:8080/agent/ai-shot/narration-count/session-123" \
  -H "Content-Type: application/json"
```

### JavaScript 请求示例

```javascript
fetch('/agent/ai-shot/narration-count/session-123')
  .then(response => response.json())
  .then(data => {
    console.log('总旁白数量:', data.data.totalNarrationCount);
    console.log('章节数量:', data.data.chapters.length);
    
    data.data.chapters.forEach(chapter => {
      console.log(`章节 ${chapter.segmentName}: ${chapter.narrationCount} 个旁白`);
    });
  })
  .catch(error => console.error('请求失败:', error));
```

## 数据库依赖

该接口依赖以下数据库表：
- `ai_shot`: 分镜数据表，包含`shot_data` JSON字段
- `ai_chapter`: 章节信息表，用于获取章节名称

## 性能考虑

1. **缓存优化**: 使用Redis缓存减少数据库查询
2. **批量查询**: 一次性查询所有相关数据，避免N+1查询问题
3. **异常容错**: 单个分镜解析失败不影响整体统计结果
4. **内存优化**: 使用流式处理避免大量数据占用内存

## 注意事项

1. 只统计`line_list`中`name`字段值为"旁白"的条目
2. 如果分镜的`shot_data`为空或解析失败，该分镜不参与统计
3. 缓存数据有24小时有效期，过期后会重新计算
4. 接口支持Swagger文档，可通过`/swagger-ui.html`查看详细API文档

---

# 修改旁白音频接口文档

## 接口概述

新增了一个用于批量修改旁白音频的接口，该接口会从Redis获取旁白统计数据，根据指定的章节ID过滤旁白，然后调用TTS服务为每个旁白生成新的音频文件。

## API 端点

### 修改旁白音频

**请求方式:** `POST`

**请求路径:** `/agent/ai-shot/narration-audio/update`

**请求参数:**

```json
{
  "sessionId": "session-123",
  "segmentIds": ["segment-1", "segment-2"],
  "soundId": 456
}
```

**请求字段说明:**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sessionId | String | 是 | 会话ID |
| segmentIds | Array | 否 | 章节ID数组，如果为空则处理所有章节 |
| soundId | Long | 否 | 音色ID，如果不传则使用会话默认音色 |

**响应格式:**

```json
{
  "success": true,
  "data": {
    "sessionId": "session-123",
    "processedChapterCount": 2,
    "successCount": 5,
    "failureCount": 1,
    "processDetails": [
      {
        "segmentId": "segment-1",
        "segmentName": "第一章",
        "shotId": "C01-SC01-25",
        "narrationId": 1,
        "narrationLine": "希望这些建议能帮助你更好地面对经济萧条。我们一起加油！",
        "status": "SUCCESS",
        "audioUrl": "http://example.com/audio/tts_123.mp3",
        "duration": 5000,
        "recordId": 123,
        "errorMessage": null
      },
      {
        "segmentId": "segment-1",
        "segmentName": "第一章",
        "shotId": "C01-SC01-26",
        "narrationId": 1,
        "narrationLine": "接下来我们来看看具体的应对策略。",
        "status": "FAILED",
        "audioUrl": null,
        "duration": null,
        "recordId": null,
        "errorMessage": "TTS生成失败，返回结果为空"
      }
    ]
  }
}
```

**响应字段说明:**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| sessionId | String | 会话ID |
| processedChapterCount | Integer | 处理的章节数量 |
| successCount | Integer | 成功生成的音频数量 |
| failureCount | Integer | 失败的音频数量 |
| processDetails | Array | 处理详情列表 |
| processDetails[].segmentId | String | 章节ID |
| processDetails[].segmentName | String | 章节名称 |
| processDetails[].shotId | String | 分镜ID |
| processDetails[].narrationId | Integer | 旁白ID |
| processDetails[].narrationLine | String | 旁白内容 |
| processDetails[].status | String | 处理状态：SUCCESS-成功，FAILED-失败 |
| processDetails[].audioUrl | String | 生成的音频URL |
| processDetails[].duration | Long | 音频时长（毫秒） |
| processDetails[].recordId | Long | TTS记录ID |
| processDetails[].errorMessage | String | 错误信息（失败时） |

## 功能特性

### 1. 数据获取逻辑
- 优先从Redis获取旁白统计数据
- 如果Redis中没有数据，自动调用旁白统计接口重新计算
- 根据章节ID过滤需要处理的旁白数据

### 2. 音色配置
- 支持指定音色ID，优先使用指定的音色
- 如果未指定音色，自动使用会话默认音色
- 如果会话未配置音色，返回错误提示

### 3. 音色验证
- 验证指定的音色ID是否存在且有效
- 查询ai_sound表确认音色可用性
- 支持系统音色和用户定制音色

### 4. 并发处理
- 支持最多5个旁白音频并发生成
- 使用信号量控制并发数量，避免系统过载
- 异步处理提高整体性能

### 5. 批量处理
- 支持指定章节ID数组，只处理指定章节的旁白
- 如果不指定章节ID，处理所有章节的旁白
- 每个旁白独立处理，单个失败不影响其他旁白

### 6. 错误处理
- 详细的错误信息记录
- 单个旁白处理失败不中断整个流程
- 返回成功和失败的统计信息

## 使用示例

### cURL 请求示例

```bash
curl -X POST "http://localhost:8080/agent/ai-shot/narration-audio/update" \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "session-123",
    "segmentIds": ["segment-1", "segment-2"],
    "soundId": 456
  }'
```

### JavaScript 请求示例

```javascript
const updateNarrationAudio = async (sessionId, segmentIds, options = {}) => {
  const requestBody = {
    sessionId,
    segmentIds,
    ...options
  };

  try {
    const response = await fetch('/agent/ai-shot/narration-audio/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();

    if (result.success) {
      console.log(`处理完成: 成功${result.data.successCount}个，失败${result.data.failureCount}个`);
      return result.data;
    } else {
      throw new Error(result.message || '请求失败');
    }
  } catch (error) {
    console.error('修改旁白音频失败:', error);
    throw error;
  }
};

// 使用示例
updateNarrationAudio('session-123', ['segment-1'], {
  soundId: 456
}).then(result => {
  console.log('处理结果:', result);
});
```

## 业务流程

1. **参数验证**: 检查会话ID是否为空
2. **获取旁白数据**: 从Redis获取旁白统计数据，如果不存在则重新计算
3. **章节过滤**: 根据章节ID数组过滤需要处理的章节
4. **音色验证**: 验证指定音色或会话默认音色是否存在
5. **并发处理**: 使用信号量控制最多5个并发TTS任务
6. **结果统计**: 统计成功和失败的数量，返回详细处理结果

## 依赖接口

- **旁白统计接口**: 当Redis中没有数据时，自动调用获取旁白统计
- **TTS生成接口**: 调用`AiCreationContentService.generateTts`生成音频
- **会话信息接口**: 获取会话默认音色配置

## 性能考虑

1. **Redis缓存**: 利用旁白统计数据的Redis缓存，避免重复计算
2. **并发处理**: 最多5个TTS任务并发执行，提高处理效率
3. **信号量控制**: 使用信号量避免系统过载，保证服务稳定性
4. **异步执行**: 使用CompletableFuture异步处理，提升响应性能
5. **异常容错**: 单个旁白处理失败不影响其他旁白的处理
6. **详细日志**: 记录详细的处理日志，便于问题排查

## 注意事项

1. TTS生成是耗时操作，已通过并发处理优化性能
2. 最多支持5个并发TTS任务，避免系统过载
3. 音色ID必须存在且有效，会自动验证ai_sound表
4. 处理结果包含详细的成功和失败信息，便于前端展示
5. 接口支持Swagger文档，可通过`/swagger-ui.html`查看详细API文档
