# 旁白统计接口文档

## 接口概述

新增了一个用于计算会话下所有章节分镜旁白数量的接口，该接口会解析分镜数据中的`shot_data`字段，统计其中`line_list`里面`name`为"旁白"的条目数量，并将结果缓存到Redis中。

## API 端点

### 计算会话旁白数量

**请求方式:** `GET`

**请求路径:** `/agent/ai-shot/narration-count/{sessionId}`

**路径参数:**
- `sessionId` (String, 必填): 会话ID

**响应格式:**

```json
{
  "success": true,
  "data": {
    "sessionId": "session-123",
    "totalNarrationCount": 5,
    "chapters": [
      {
        "segmentId": "segment-1",
        "segmentName": "第一章",
        "narrationCount": 3,
        "narrationDetails": [
          {
            "shotId": "C01-SC01-25",
            "narrationId": 1,
            "narrationLine": "希望这些建议能帮助你更好地面对经济萧条。我们一起加油！"
          },
          {
            "shotId": "C01-SC01-26",
            "narrationId": 1,
            "narrationLine": "接下来我们来看看具体的应对策略。"
          },
          {
            "shotId": "C01-SC01-27",
            "narrationId": 2,
            "narrationLine": "这是另一段旁白内容。"
          }
        ]
      },
      {
        "segmentId": "segment-2",
        "segmentName": "第二章",
        "narrationCount": 2,
        "narrationDetails": [
          {
            "shotId": "C02-SC01-01",
            "narrationId": 1,
            "narrationLine": "第二章的旁白内容。"
          },
          {
            "shotId": "C02-SC01-02",
            "narrationId": 1,
            "narrationLine": "继续第二章的内容。"
          }
        ]
      }
    ]
  }
}
```

**响应字段说明:**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| sessionId | String | 会话ID |
| totalNarrationCount | Integer | 总旁白数量 |
| chapters | Array | 章节旁白统计列表 |
| chapters[].segmentId | String | 章节ID |
| chapters[].segmentName | String | 章节名称 |
| chapters[].narrationCount | Integer | 该章节的旁白数量 |
| chapters[].narrationDetails | Array | 旁白详情列表 |
| narrationDetails[].shotId | String | 分镜ID |
| narrationDetails[].narrationId | Integer | 旁白ID（来自line_list中的id字段） |
| narrationDetails[].narrationLine | String | 旁白内容（来自line_list中的line字段） |

## 功能特性

### 1. 数据解析逻辑
- 根据`sessionId`查询`ai_shot`表获取所有分镜数据
- 解析每个分镜的`shot_data` JSON字段
- 遍历`shot_data.line_list`数组，统计`name`字段值为"旁白"的条目
- 记录旁白的分镜ID、旁白ID和旁白内容

### 2. Redis缓存机制
- **缓存Key格式:** `NARRATION_COUNT:SESSION:{sessionId}`
- **缓存时间:** 24小时
- **缓存策略:** 优先从缓存获取，缓存不存在时查询数据库并更新缓存

### 3. 错误处理
- 参数校验：会话ID不能为空
- JSON解析异常：单个分镜解析失败不影响整体流程
- Redis异常：缓存操作失败不影响主流程

## 使用示例

### cURL 请求示例

```bash
curl -X GET "http://localhost:8080/agent/ai-shot/narration-count/session-123" \
  -H "Content-Type: application/json"
```

### JavaScript 请求示例

```javascript
fetch('/agent/ai-shot/narration-count/session-123')
  .then(response => response.json())
  .then(data => {
    console.log('总旁白数量:', data.data.totalNarrationCount);
    console.log('章节数量:', data.data.chapters.length);
    
    data.data.chapters.forEach(chapter => {
      console.log(`章节 ${chapter.segmentName}: ${chapter.narrationCount} 个旁白`);
    });
  })
  .catch(error => console.error('请求失败:', error));
```

## 数据库依赖

该接口依赖以下数据库表：
- `ai_shot`: 分镜数据表，包含`shot_data` JSON字段
- `ai_chapter`: 章节信息表，用于获取章节名称

## 性能考虑

1. **缓存优化**: 使用Redis缓存减少数据库查询
2. **批量查询**: 一次性查询所有相关数据，避免N+1查询问题
3. **异常容错**: 单个分镜解析失败不影响整体统计结果
4. **内存优化**: 使用流式处理避免大量数据占用内存

## 注意事项

1. 只统计`line_list`中`name`字段值为"旁白"的条目
2. 如果分镜的`shot_data`为空或解析失败，该分镜不参与统计
3. 缓存数据有24小时有效期，过期后会重新计算
4. 接口支持Swagger文档，可通过`/swagger-ui.html`查看详细API文档
