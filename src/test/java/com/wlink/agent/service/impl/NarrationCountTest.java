package com.wlink.agent.service.impl;

import com.alibaba.fastjson.JSON;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.model.req.NarrationCountReq;
import com.wlink.agent.model.res.NarrationCountRes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class NarrationCountTest {

    @Mock
    private AiShotMapper aiShotMapper;

    @Mock
    private AiChapterMapper aiChapterMapper;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<Object> bucket;

    @InjectMocks
    private AiShotServiceImpl aiShotService;

    private String testSessionId = "test-session-123";

    @BeforeEach
    void setUp() {
        lenient().when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        lenient().when(bucket.get()).thenReturn(null); // 模拟Redis中没有缓存
    }

    @Test
    void testGetNarrationCount_NarrationMode() {
        // 准备测试数据 - 旁白统计模式
        String shotDataWithNarration = """
            {
                "shotId": null,
                "id": "C01-SC01-25",
                "type": "Medium Shot + Eye-Level",
                "movement": "Dolly Out",
                "line_list": [
                    {
                        "name": "旁白",
                        "line": "这是一段旁白内容",
                        "charID": "NARRATOR001",
                        "id": 1,
                        "voice": null
                    },
                    {
                        "name": "赵高",
                        "line": "陛下，臣特意进献一匹千里良驹",
                        "charID": "ZhaoGao_aA3p",
                        "id": 2,
                        "voice": null
                    }
                ]
            }
            """;

        // Mock分镜数据
        AiShotPo shot = new AiShotPo();
        shot.setId(1L);
        shot.setSessionId(testSessionId);
        shot.setSegmentId("segment-1");
        shot.setShotId("C01-SC01-25");
        shot.setShotData(shotDataWithNarration);

        List<AiShotPo> shotList = Arrays.asList(shot);

        // Mock章节数据
        AiChapterPo chapter = new AiChapterPo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("第一章");
        List<AiChapterPo> chapterList = Arrays.asList(chapter);

        // 设置Mock行为
        when(aiShotMapper.selectList(any())).thenReturn(shotList);
        when(aiChapterMapper.selectList(any())).thenReturn(chapterList);

        // 执行测试 - 旁白统计
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId(testSessionId);
        // characterId为空，统计旁白

        NarrationCountRes result = aiShotService.getNarrationCount(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertNull(result.getCharacterId());
        assertEquals("narration", result.getCountType());
        assertEquals(1, result.getTotalCount()); // 只有1个旁白
        assertEquals(1, result.getChapters().size());

        NarrationCountRes.ChapterNarrationInfo chapterInfo = result.getChapters().get(0);
        assertEquals("segment-1", chapterInfo.getSegmentId());
        assertEquals("第一章", chapterInfo.getSegmentName());
        assertEquals(1, chapterInfo.getCount());
        assertEquals(1, chapterInfo.getDetails().size());

        NarrationCountRes.NarrationDetail detail = chapterInfo.getDetails().get(0);
        assertEquals("C01-SC01-25", detail.getShotId());
        assertEquals(1, detail.getId());
        assertEquals("这是一段旁白内容", detail.getContent());
        assertNull(detail.getCharacterId()); // 旁白统计时角色信息为空
    }

    @Test
    void testGetNarrationCount_CharacterMode() {
        // 准备测试数据 - 角色统计模式
        String shotDataWithCharacter = """
            {
                "shotId": null,
                "id": "C01-SC03-02",
                "type": "Close-Up + Eye-Level",
                "movement": "Static Shot",
                "line_list": [
                    {
                        "name": "旁白",
                        "line": "这是一段旁白内容",
                        "charID": "NARRATOR001",
                        "id": 1,
                        "voice": null
                    },
                    {
                        "name": "赵高",
                        "line": "陛下，臣特意进献一匹千里良驹，此马神骏异常！",
                        "charID": "ZhaoGao_aA3p",
                        "id": 2,
                        "voice": null
                    },
                    {
                        "name": "赵高",
                        "line": "请陛下过目！",
                        "charID": "ZhaoGao_aA3p",
                        "id": 3,
                        "voice": null
                    }
                ]
            }
            """;

        // Mock分镜数据
        AiShotPo shot = new AiShotPo();
        shot.setId(1L);
        shot.setSessionId(testSessionId);
        shot.setSegmentId("segment-1");
        shot.setShotId("C01-SC03-02");
        shot.setShotData(shotDataWithCharacter);

        List<AiShotPo> shotList = Arrays.asList(shot);

        // Mock章节数据
        AiChapterPo chapter = new AiChapterPo();
        chapter.setSegmentId("segment-1");
        chapter.setSegmentName("第一章");
        List<AiChapterPo> chapterList = Arrays.asList(chapter);

        // 设置Mock行为
        when(aiShotMapper.selectList(any())).thenReturn(shotList);
        when(aiChapterMapper.selectList(any())).thenReturn(chapterList);

        // 执行测试 - 角色统计
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId(testSessionId);
        req.setCharacterId("ZhaoGao_aA3p"); // 统计赵高的音频

        NarrationCountRes result = aiShotService.getNarrationCount(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals("ZhaoGao_aA3p", result.getCharacterId());
        assertEquals("character", result.getCountType());
        assertEquals(2, result.getTotalCount()); // 赵高有2段台词
        assertEquals(1, result.getChapters().size());

        NarrationCountRes.ChapterNarrationInfo chapterInfo = result.getChapters().get(0);
        assertEquals("segment-1", chapterInfo.getSegmentId());
        assertEquals("第一章", chapterInfo.getSegmentName());
        assertEquals(2, chapterInfo.getCount());
        assertEquals(2, chapterInfo.getDetails().size());

        // 验证第一个角色音频详情
        NarrationCountRes.NarrationDetail detail1 = chapterInfo.getDetails().get(0);
        assertEquals("C01-SC03-02", detail1.getShotId());
        assertEquals(2, detail1.getId());
        assertEquals("陛下，臣特意进献一匹千里良驹，此马神骏异常！", detail1.getContent());
        assertEquals("ZhaoGao_aA3p", detail1.getCharacterId());
        assertEquals("赵高", detail1.getCharacterName());

        // 验证第二个角色音频详情
        NarrationCountRes.NarrationDetail detail2 = chapterInfo.getDetails().get(1);
        assertEquals("C01-SC03-02", detail2.getShotId());
        assertEquals(3, detail2.getId());
        assertEquals("请陛下过目！", detail2.getContent());
        assertEquals("ZhaoGao_aA3p", detail2.getCharacterId());
        assertEquals("赵高", detail2.getCharacterName());
    }

    @Test
    void testGetNarrationCount_EmptySession() {
        // Mock空的分镜列表
        when(aiShotMapper.selectList(any())).thenReturn(Arrays.asList());

        // 执行测试
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId(testSessionId);

        NarrationCountRes result = aiShotService.getNarrationCount(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSessionId, result.getSessionId());
        assertEquals("narration", result.getCountType());
        assertEquals(0, result.getTotalCount());
        assertTrue(result.getChapters().isEmpty());
    }

    @Test
    void testGetNarrationCount_InvalidSessionId() {
        // 测试空的sessionId
        NarrationCountReq req = new NarrationCountReq();
        req.setSessionId("");

        assertThrows(Exception.class, () -> {
            aiShotService.getNarrationCount(req);
        });

        req.setSessionId(null);
        assertThrows(Exception.class, () -> {
            aiShotService.getNarrationCount(req);
        });
    }
}
