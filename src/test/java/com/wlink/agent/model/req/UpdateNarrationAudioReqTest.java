package com.wlink.agent.model.req;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;

/**
 * UpdateNarrationAudioReq 参数验证测试
 */
public class UpdateNarrationAudioReqTest {

    @Test
    public void testRequestParameters() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        
        // 测试基本参数设置
        req.setSessionId("test-session-123");
        req.setSegmentIds(Arrays.asList("segment-1", "segment-2"));
        req.setSoundId(456L);
        
        // 验证参数
        assertEquals("test-session-123", req.getSessionId());
        assertEquals(2, req.getSegmentIds().size());
        assertEquals("segment-1", req.getSegmentIds().get(0));
        assertEquals("segment-2", req.getSegmentIds().get(1));
        assertEquals(456L, req.getSoundId());
    }

    @Test
    public void testOptionalParameters() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        
        // 测试可选参数
        req.setSessionId("test-session");
        req.setSoundId(null); // 音色ID可以为空，使用会话默认音色
        req.setSegmentIds(null); // 章节ID可以为空，处理所有章节
        
        assertEquals("test-session", req.getSessionId());
        assertNull(req.getSoundId());
        assertNull(req.getSegmentIds());
    }

    @Test
    public void testEmptySegmentIds() {
        UpdateNarrationAudioReq req = new UpdateNarrationAudioReq();
        
        req.setSessionId("test-session");
        req.setSegmentIds(Arrays.asList()); // 空数组
        
        assertEquals("test-session", req.getSessionId());
        assertTrue(req.getSegmentIds().isEmpty());
    }
}
