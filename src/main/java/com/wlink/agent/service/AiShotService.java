package com.wlink.agent.service;

import com.wlink.agent.model.req.ChapterDesignContentQueryReq;
import com.wlink.agent.model.req.ShotUpdateReq;
import com.wlink.agent.model.res.ChapterDesignContentRes;
import com.wlink.agent.model.res.ChapterRes;
import com.wlink.agent.model.res.ShotRes;

import java.util.List;

/**
 * 分镜服务接口
 */
public interface AiShotService {

    /**
     * 根据会话ID查询章节列表
     *
     * @param conversationId 会话ID
     * @return 章节列表
     */
    List<ChapterRes> listChapters(String conversationId);

    /**
     * 根据会话ID和章节ID查询分镜列表
     *
     * @param conversationId 会话ID
     * @param segmentId 章节ID
     * @return 分镜列表
     */
    List<ShotRes> listShots(String conversationId, String segmentId);

    /**
     * 根据分镜ID修改分镜数据
     *
     * @param req 分镜修改请求
     * @return 更新后的分镜数据
     */
    ShotRes updateShot(ShotUpdateReq req);

    /**
     * 根据章节ID查询会话设计内容
     *
     * @param req 查询请求
     * @return 设计内容
     */
    ChapterDesignContentRes getChapterDesignContent(ChapterDesignContentQueryReq req);
} 