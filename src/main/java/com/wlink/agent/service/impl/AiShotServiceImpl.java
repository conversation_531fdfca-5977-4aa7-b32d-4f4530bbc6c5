package com.wlink.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.dao.mapper.AgentSoundMapper;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiCreationContentMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiSoundEffectsRecordMapper;
import com.wlink.agent.dao.mapper.AiTtsRecordMapper;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiSoundEffectsRecordPo;
import com.wlink.agent.dao.po.AiTtsRecordPo;
import com.wlink.agent.enums.TaskStatus;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.model.dto.ContentKeyValueDto;
import com.wlink.agent.model.req.ChapterDesignContentQueryReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.ShotUpdateReq;
import com.wlink.agent.model.res.ChapterDesignContentRes;
import com.wlink.agent.model.req.NarrationCountReq;
import com.wlink.agent.model.req.TtsGenerateReq;
import com.wlink.agent.model.req.UpdateNarrationAudioReq;
import com.wlink.agent.model.res.ChapterRes;
import com.wlink.agent.model.res.NarrationCountRes;
import com.wlink.agent.model.res.ShotRes;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.model.res.UpdateNarrationAudioRes;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.service.AiCreationContentService;
import com.wlink.agent.service.AiShotService;
import com.wlink.agent.utils.I18nMessageUtils;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 分镜服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiShotServiceImpl implements AiShotService {

    private final AiChapterMapper aiChapterMapper;
    private final AiShotMapper aiShotMapper;
    private final AiCreationSessionMapper aiCreationSessionMapper;
    private final AiCreationContentService aiCreationContentService;
    private final AiCreationContentMapper aiCreationContentMapper;
    private final AiImageTaskQueueMapper aiImageTaskQueueMapper;
    private final AiTtsRecordMapper aiTtsRecordMapper;
    private final AiSoundEffectsRecordMapper aiSoundEffectsRecordMapper;
    private final RedissonClient redissonClient;
    private final AgentSoundMapper agentSoundMapper;
    private final Executor taskExecutor;

    @Override
    public List<ChapterRes> listChapters(String conversationId) {
        log.info("查询章节列表: conversationId={}", conversationId);

        // 参数校验
        if (StringUtils.isBlank(conversationId)) {
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.PARAM_ERROR.getMsg()));
        }

        // 查询章节数据
        List<AiChapterPo> chapters = aiChapterMapper.selectList(
                new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, conversationId)
                        .eq(AiChapterPo::getDelFlag, 0)
                        .orderByAsc(AiChapterPo::getSegmentId)
        );

        if (chapters.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询所有分镜数据，用于统计状态
        List<AiShotPo> allShots = aiShotMapper.selectList(
                new LambdaQueryWrapper<AiShotPo>()
                        .eq(AiShotPo::getSessionId, conversationId)
                        .eq(AiShotPo::getDelFlag, 0)
        );

        // 按章节ID分组统计分镜数据
        Map<String, List<AiShotPo>> shotsBySegmentId = allShots.stream()
                .collect(Collectors.groupingBy(AiShotPo::getSegmentId));

        // 转换为响应对象，并统计资源完成情况
        return chapters.stream()
                .map(chapter -> {
                    ChapterRes res = convertToChapterRes(chapter);

                    // 获取该章节下的所有分镜
                    List<AiShotPo> chapterShots = shotsBySegmentId.getOrDefault(chapter.getSegmentId(), Collections.emptyList());
                    res.setTotalShotCount(chapterShots.size());

                    // 设置章节第一个分镜的图片
                    if (!chapterShots.isEmpty()) {
                        AiShotPo firstShot = chapterShots.stream()
                                .sorted((s1, s2) -> {
                                    // 先按场景ID排序，再按分镜ID排序
                                    if (s1.getSceneId() == null || s2.getSceneId() == null) {
                                        return s1.getShotId() == null ? 1 : (s2.getShotId() == null ? -1 : s1.getShotId().compareTo(s2.getShotId()));
                                    }
                                    int sceneCompare = s1.getSceneId().compareTo(s2.getSceneId());
                                    return sceneCompare != 0 ? sceneCompare :
                                            (s1.getShotId() == null ? 1 : (s2.getShotId() == null ? -1 : s1.getShotId().compareTo(s2.getShotId())));
                                })
                                .findFirst()
                                .orElse(null);

                        if (StringUtils.isNotBlank(firstShot.getShotData())) {
                            try {
                                ShotSaveReq.ShotGroupsDTO.ShotsDTO shotData = JSON.parseObject(
                                        firstShot.getShotData(),
                                        ShotSaveReq.ShotGroupsDTO.ShotsDTO.class
                                );

                                // 设置第一个分镜的图片
                                // 查询图片状态
                                AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(
                                        new LambdaQueryWrapper<AiImageTaskQueuePo>()
                                                .eq(AiImageTaskQueuePo::getSessionId, chapter.getSessionId())
                                                .eq(AiImageTaskQueuePo::getContentId, shotData.getId())
                                                .eq(AiImageTaskQueuePo::getContentType, 4)
                                                .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                                                .eq(AiImageTaskQueuePo::getTaskStatus, TaskStatus.COMPLETED.getValue())
                                                .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                                                .last("limit 1"));


                                if (null != aiImageTaskQueuePo) {
                                    String imageUrl = aiImageTaskQueuePo.getImageResult();
                                    // 如果图片URL不包含前缀，则添加前缀
                                    if (!imageUrl.startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)) {
                                        imageUrl = MediaUrlPrefixUtil.getMediaUrl(imageUrl);
                                    }
                                    res.setFirstShotImage(imageUrl);
                                }
                            } catch (Exception e) {
                                log.error("解析第一个分镜数据失败: segmentId={}, shotId={}, error={}",
                                        chapter.getSegmentId(), firstShot.getShotId(), e.getMessage());
                            }
                        }
                    }

                    // 解析每个分镜的数据，统计资源完成情况
                    for (AiShotPo shot : chapterShots) {
                        if (StringUtils.isNotBlank(shot.getShotData())) {
                            try {
                                ShotSaveReq.ShotGroupsDTO.ShotsDTO shotData = JSON.parseObject(
                                        shot.getShotData(),
                                        ShotSaveReq.ShotGroupsDTO.ShotsDTO.class
                                );

                                // 检查图片是否完成
                                if (StringUtils.isNotBlank(shotData.getImage()) &&
                                        shotData.getImage().endsWith(".png")) {
                                    res.setCompletedImageCount(res.getCompletedImageCount() + 1);
                                } else {
                                    // 查询图片状态
                                    AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(
                                            new LambdaQueryWrapper<AiImageTaskQueuePo>()
                                                    .eq(AiImageTaskQueuePo::getSessionId, shot.getSessionId())
                                                    .eq(AiImageTaskQueuePo::getContentId, shot.getShotId())
                                                    .eq(AiImageTaskQueuePo::getContentType, 4)
                                                    .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                                                    .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                                                    .last("limit 1"));
                                    if (aiImageTaskQueuePo != null) {
                                        res.setCompletedImageCount(res.getCompletedImageCount() + 1);
                                    }
                                }

                                // 检查音频是否完成
                                if (StringUtils.isNotBlank(shotData.getVoice()) &&
                                        shotData.getVoice().endsWith(".wav")) {
                                    res.setCompletedVoiceCount(res.getCompletedVoiceCount() + 1);
                                } else {
                                    AiTtsRecordPo narration = aiTtsRecordMapper.selectOne(
                                            new LambdaQueryWrapper<AiTtsRecordPo>()
                                                    .eq(AiTtsRecordPo::getConversationId, shot.getSessionId())
                                                    .eq(AiTtsRecordPo::getContentId, shot.getShotId())
                                                    .eq(AiTtsRecordPo::getType, "narration")
                                                    .eq(AiTtsRecordPo::getStatus, 1)
                                                    .orderByDesc(AiTtsRecordPo::getCreateTime)
                                                    .last("limit 1"));
                                    if (narration != null) {
                                        res.setCompletedVoiceCount(res.getCompletedVoiceCount() + 1);
                                    }
                                }

                                // 检查旁白是否完成
                                if (StringUtils.isNotBlank(shotData.getNarration())) {
                                    res.setCompletedNarrationCount(res.getCompletedNarrationCount() + 1);
                                }
                            } catch (Exception e) {
                                log.error("解析分镜数据失败: segmentId={}, shotId={}, error={}",
                                        chapter.getSegmentId(), shot.getShotId(), e.getMessage());
                            }
                        }
                    }

                    // 判断是否全部完成
                    if (res.getTotalShotCount() > 0 &&
                            res.getCompletedImageCount() == res.getTotalShotCount() &&
                            res.getCompletedVoiceCount() == res.getTotalShotCount() &&
                            res.getCompletedNarrationCount() == res.getTotalShotCount()) {
                        res.setIsAllCompleted(true);
                    }

                    return res;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<ShotRes> listShots(String conversationId, String segmentId) {
        log.info("查询分镜列表: conversationId={}, segmentId={}", conversationId, segmentId);

        // 参数校验
        if (StringUtils.isBlank(conversationId)) {
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.PARAM_ERROR.getMsg()));
        }
        if (StringUtils.isBlank(segmentId)) {
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.PARAM_ERROR.getMsg()));
        }

        // 查询分镜数据
        List<AiShotPo> shots = aiShotMapper.selectList(
                new LambdaQueryWrapper<AiShotPo>()
                        .eq(AiShotPo::getSessionId, conversationId)
                        .eq(AiShotPo::getSegmentId, segmentId)
                        .eq(AiShotPo::getDelFlag, 0)
                        .orderByAsc(AiShotPo::getQueue)
                        .orderByAsc(AiShotPo::getSceneId)
                        .orderByAsc(AiShotPo::getShotId)
        );

        // 转换为响应对象
        return shots.stream()
                .map(this::convertToShotRes)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShotRes updateShot(ShotUpdateReq req) {
        log.info("修改分镜数据: shotId={}", req.getShotId());


        if (req.getShotId() == null) {
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), I18nMessageUtils.getMessage(ErrorCodeEnum.PARAM_ERROR.getMsg()));
        }
        // 查询分镜数据
        AiShotPo shot = aiShotMapper.selectById(req.getShotId());

        if (shot == null) {
            log.error("分镜数据不存在: shotId={}", req.getShotId());
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), "分镜数据不存在");
        }

        // 获取旧的分镜数据，用于比较旁白内容是否变更
        ShotSaveReq.ShotGroupsDTO.ShotsDTO oldShotData = null;
        if (StringUtils.isNotBlank(shot.getShotData())) {
            try {
                oldShotData = JSON.parseObject(shot.getShotData(), ShotSaveReq.ShotGroupsDTO.ShotsDTO.class);
            } catch (Exception e) {
                log.error("解析旧分镜数据失败: shotId={}, error={}", req.getShotId(), e.getMessage());
            }
        }

        req.setImage(StringUtils.isNotBlank(req.getImage()) ? req.getImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : null);
        req.setVoice(StringUtils.isNotBlank(req.getVoice()) ? req.getVoice().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "") : null);

        // 判断旁白是否变更，需要重新生成音频
        boolean narrationChanged = false;
        if (oldShotData != null) {
            String oldNarration = oldShotData.getNarration();
            String newNarration = req.getNarration();

            // 如果旁白内容变更，且新的旁白不为空，则需要重新生成音频
            if (!StringUtils.equals(oldNarration, newNarration) && StringUtils.isNotBlank(newNarration)) {
                narrationChanged = true;
                log.info("旁白内容已变更，需要重新生成音频: shotId={}", req.getShotId());
            }
        }

        // 如果旁白变更，需要重新生成音频
        if (narrationChanged) {
            try {
                Long voicdeId = null;
                // 查询会话信息，获取音色ID
                AiTtsRecordPo narration = aiTtsRecordMapper.selectOne(new LambdaQueryWrapper<AiTtsRecordPo>()
                        .eq(AiTtsRecordPo::getConversationId, shot.getSessionId())
                        .eq(AiTtsRecordPo::getContentId, shot.getShotId())
                        .eq(AiTtsRecordPo::getType, "narration")
                        .eq(AiTtsRecordPo::getStatus, 1)
                        .orderByDesc(AiTtsRecordPo::getCreateTime)
                        .last("limit 1"));
                if (narration != null) {
                    voicdeId = narration.getVoiceId();
                } else {
                    AiCreationSessionPo sessionPo = aiCreationSessionMapper.selectOne(
                            new LambdaQueryWrapper<AiCreationSessionPo>()
                                    .eq(AiCreationSessionPo::getSessionId, shot.getSessionId())
                                    .eq(AiCreationSessionPo::getDelFlag, 0)
                                    .last("LIMIT 1"));
                    if (sessionPo != null) {
                        voicdeId = sessionPo.getSoundId();
                    }

                }
                if (voicdeId != null) {
                    // 构建TTS请求
                    TtsGenerateReq ttsReq = new TtsGenerateReq();
                    ttsReq.setConversationId(shot.getSessionId());
                    ttsReq.setContentId(req.getId());
                    ttsReq.setSoundType("narration");
                    ttsReq.setVoiceId(String.valueOf(voicdeId));
                    ttsReq.setText(req.getNarration());

                    // 调用TTS生成服务
                    TtsGenerateRes ttsRes = aiCreationContentService.generateTts(ttsReq);

                    // 更新分镜的音频URL
                    if (StringUtils.isNotBlank(ttsRes.getAudioUrl())) {
                        String audioUrl = ttsRes.getAudioUrl().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, "");
                        req.setVoice(audioUrl);
                        log.info("音频生成成功: shotId={}, audioUrl={}", req.getShotId(), ttsRes.getAudioUrl());
                    }
                } else {
                    log.warn("未找到会话信息或会话未配置音色ID: conversationId={}", shot.getSessionId());
                }
            } catch (Exception e) {
                log.error("生成音频失败: shotId={}, error={}", req.getShotId(), e.getMessage());
                // 不阻断后续流程，仅记录错误
            }
        }

        // 更新分镜数据

        shot.setShotData(JSON.toJSONString(req));
        shot.setUpdateTime(new Date());
        aiShotMapper.updateById(shot);

        // 构建响应对象，返回更新后的分镜数据
        ShotRes result = convertToShotRes(shot);
        if (null == result) {
            throw new BizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "分镜数据转换失败");
        }
        // 对于新生成的音频，更新URL前缀
        if (StringUtils.isNotBlank(result.getVoice())
                && !result.getVoice().startsWith(MediaUrlPrefixUtil.MEDIA_URL_PREFIX)) {
            result.setVoice(MediaUrlPrefixUtil.getMediaUrl(result.getVoice()));
        }
        log.info("分镜数据更新成功: shotId={}", req.getShotId());
        return result;
    }

    /**
     * 将章节PO对象转换为响应对象
     */
    private ChapterRes convertToChapterRes(AiChapterPo chapter) {
        ChapterRes res = new ChapterRes();
        res.setId(chapter.getId());
        res.setSegmentId(chapter.getSegmentId());
        res.setSegmentName(chapter.getSegmentName());
        res.setSceneCount(chapter.getSceneCount());
        res.setCreateTime(chapter.getCreateTime());
        res.setUpdateTime(chapter.getUpdateTime());
        return res;
    }

    /**
     * 将分镜PO对象转换为响应对象
     */
    private ShotRes convertToShotRes(AiShotPo shot) {
        ShotRes shotRes = null;
        // 解析分镜数据
        if (StringUtils.isNotBlank(shot.getShotData())) {
            try {
                shotRes = JSON.parseObject(
                        shot.getShotData(),
                        ShotRes.class
                );
                shotRes.setImage(StringUtils.isNotBlank(shotRes.getImage()) ? MediaUrlPrefixUtil.getMediaUrl(shotRes.getImage()) : null);
                // 查询图片状态
                AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(
                        new LambdaQueryWrapper<AiImageTaskQueuePo>()
                                .eq(AiImageTaskQueuePo::getSessionId, shot.getSessionId())
                                .eq(AiImageTaskQueuePo::getContentId, shot.getShotId())
                                .eq(AiImageTaskQueuePo::getContentType, 4)
                                .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                                .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                                .last("limit 1")
                );

                if (aiImageTaskQueuePo != null) {
                    shotRes.setImageStatus(aiImageTaskQueuePo.getTaskStatus());
                    if (Objects.equals(aiImageTaskQueuePo.getTaskStatus(), TaskStatus.COMPLETED.getValue())) {
                        if (StringUtils.isBlank(shotRes.getImage()) || !shotRes.getImage().endsWith(".png")) {
                            shotRes.setImage(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
                        }
                    }
                } else {
                    if (StringUtils.isBlank(shotRes.getImage()) || !shotRes.getImage().endsWith(".png")) {
                        shotRes.setImageStatus(TaskStatus.PENDING_SUBMISSION.getValue());
                    } else {
                        shotRes.setImageStatus(TaskStatus.COMPLETED.getValue());
                    }
                }
                // 查询语音信息
                List<AiTtsRecordPo> allTtsRecords = aiTtsRecordMapper.selectList(
                        new LambdaQueryWrapper<AiTtsRecordPo>()
                                .eq(AiTtsRecordPo::getConversationId, shot.getSessionId())
                                .eq(AiTtsRecordPo::getContentId, shot.getShotId())
                                .eq(AiTtsRecordPo::getStatus, 1)
                                .orderByDesc(AiTtsRecordPo::getCreateTime)
                );

                // 根据audio_index去重，同样的index只保留最近的一条
                List<AiTtsRecordPo> aiTtsRecordPos = allTtsRecords.stream()
                        .collect(Collectors.groupingBy(AiTtsRecordPo::getAudioIndex))
                        .values()
                        .stream()
                        .map(records -> records.stream()
                                .max(Comparator.comparing(AiTtsRecordPo::getCreateTime))
                                .orElse(null))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                List<AiSoundEffectsRecordPo> aiSoundEffectsRecordPos = aiSoundEffectsRecordMapper.selectList(new LambdaQueryWrapper<AiSoundEffectsRecordPo>()
                        .eq(AiSoundEffectsRecordPo::getSessionId, shot.getSessionId())
                        .eq(AiSoundEffectsRecordPo::getContentId, shot.getShotId())
                        .eq(AiSoundEffectsRecordPo::getGenerationStatus, "SUCCESS")
                        .orderByDesc(AiSoundEffectsRecordPo::getCreateTime));
                Map<Integer, AiSoundEffectsRecordPo> effectsRecordPoMap;
                if (CollUtil.isNotEmpty(aiSoundEffectsRecordPos)) {
                    effectsRecordPoMap = aiSoundEffectsRecordPos.stream().collect(Collectors.toMap(AiSoundEffectsRecordPo::getAudioIndex, Function.identity(), (existing, replacement) -> existing));
                } else {
                    effectsRecordPoMap = null;
                }

                Map<Integer, AiTtsRecordPo> ttsRecordMap;
                if (CollUtil.isNotEmpty(aiTtsRecordPos)) {
                    // 创建一个Map，以index为key，方便快速查找
                    ttsRecordMap = aiTtsRecordPos.stream()
                            .collect(Collectors.toMap(AiTtsRecordPo::getAudioIndex, Function.identity(), (existing, replacement) -> existing));
                } else {
                    ttsRecordMap = null;
                }
                List<ShotRes.ShotLines> lines = shotRes.getLineList();
                if (CollUtil.isNotEmpty(lines)) {
                    // 遍历lines集合，根据id匹配对应的AiTtsRecordPo
                    lines.forEach(shotLine -> {
                        if (ttsRecordMap != null && !Objects.equals(shotLine.getName(),"音效")) {
                            AiTtsRecordPo matchedTtsRecord = ttsRecordMap.get(shotLine.getId());
                            if (matchedTtsRecord != null) {
                                // 找到匹配的记录，可以进行相应的处理
                                // 例如：设置语音URL
                                if (StringUtils.isNotBlank(matchedTtsRecord.getAudioUrl())) {
                                    shotLine.setVoice(MediaUrlPrefixUtil.getMediaUrl(matchedTtsRecord.getAudioUrl()));
                                    shotLine.setLine(matchedTtsRecord.getText());
                                    shotLine.setDuration(matchedTtsRecord.getAudioLength());
                                    shotLine.setVoiceType(1);

                                }
                                // 可以根据需要设置其他字段
                                log.debug("匹配成功: shotLine.id={}, ttsRecord.index={}, audioUrl={}",
                                        shotLine.getId(), matchedTtsRecord.getAudioIndex(), matchedTtsRecord.getAudioUrl());
                            }
                        }
                        if (effectsRecordPoMap != null && Objects.equals(shotLine.getName(),"音效")) {
                            AiSoundEffectsRecordPo aiSoundEffectsRecordPo = effectsRecordPoMap.get(shotLine.getId());
                            if (aiSoundEffectsRecordPo != null) {
                                shotLine.setVoice(MediaUrlPrefixUtil.getMediaUrl(aiSoundEffectsRecordPo.getOssAudioUrl()));
                                shotLine.setLine(aiSoundEffectsRecordPo.getPrompt());
                                shotLine.setDuration(aiSoundEffectsRecordPo.getDuration() * 1000L);
                                shotLine.setVoiceType(2);
                            }
                        }
                    });
                } else if (CollUtil.isEmpty(lines) && CollUtil.isNotEmpty(aiTtsRecordPos)) {
                    String audioUrl = aiTtsRecordPos.get(0).getAudioUrl();
                    ShotRes.ShotLines shotLines = new ShotRes.ShotLines();
                    shotLines.setVoice(MediaUrlPrefixUtil.getMediaUrl(audioUrl));
                    shotLines.setLine(aiTtsRecordPos.get(0).getText());
                    shotLines.setId(1);
                    shotLines.setName("旁白");
                    shotLines.setDuration(aiTtsRecordPos.get(0).getAudioLength());
                    shotRes.setLineList(List.of(shotLines));

                }
                shotRes.setShotId(shot.getId());
            } catch (Exception e) {
                log.error("解析分镜数据失败: {}", e.getMessage());
            }
        }
        return shotRes;
    }

    @Override
    public ChapterDesignContentRes getChapterDesignContent(ChapterDesignContentQueryReq req) {
        log.info("根据章节ID查询会话设计内容: chapterId={}", req.getChapterId());

        // 1. 根据章节ID查询ai_chapter表获取sessionId和segmentId
        AiChapterPo chapterPo = aiChapterMapper.selectById(req.getChapterId());
        if (chapterPo == null) {
            log.error("章节不存在: chapterId={}", req.getChapterId());
            throw new BizException("章节不存在");
        }

        String sessionId = chapterPo.getSessionId();
        String segmentId = chapterPo.getSegmentId();

        log.debug("查询到章节信息: sessionId={}, segmentId={}, segmentName={}",
                sessionId, segmentId, chapterPo.getSegmentName());

        // 2. 根据sessionId和contentType=10查询ai_creation_content表
        LambdaQueryWrapper<AiCreationContentPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCreationContentPo::getSessionId, sessionId)
                .eq(AiCreationContentPo::getContentType, 10) // 10-故事设计
                .eq(AiCreationContentPo::getDelFlag, 0)
                .orderByDesc(AiCreationContentPo::getCreateTime)
                .last("LIMIT 1");

        AiCreationContentPo contentPo = aiCreationContentMapper.selectOne(queryWrapper);
        if (contentPo == null) {
            log.warn("未找到对应的设计内容: sessionId={}, contentType=10", sessionId);
            // 返回空的设计内容
            ChapterDesignContentRes response = new ChapterDesignContentRes();
            return response;
        }

        // 3. 解析contentData为键值对数组
        String matchedValue = null;
        try {
            String contentDataJson = contentPo.getContentData();
            log.debug("原始设计内容数据: {}", contentDataJson);

            // 将JSON字符串解析为键值对数组
            List<ContentKeyValueDto> keyValueList = JSON.parseArray(contentDataJson, ContentKeyValueDto.class);

            if (keyValueList != null) {
                // 4. 根据segmentId匹配key，获取对应的value
                for (ContentKeyValueDto keyValue : keyValueList) {
                    if (segmentId.equals(keyValue.getKey())) {
                        matchedValue = keyValue.getValue();
                        log.debug("找到匹配的设计内容: segmentId={}, value={}", segmentId, matchedValue);
                        break;
                    }
                }

                if (matchedValue == null) {
                    log.warn("未找到匹配的设计内容: segmentId={}, 可用的keys={}",
                            segmentId, keyValueList.stream().map(ContentKeyValueDto::getKey).collect(Collectors.toList()));
                }
            }
        } catch (Exception e) {
            log.error("解析设计内容数据失败: sessionId={}, contentData={}", sessionId, contentPo.getContentData(), e);
        }

        // 5. 构建响应对象
        ChapterDesignContentRes response = new ChapterDesignContentRes();
        response.setContent(matchedValue);
        log.info("查询章节设计内容完成: chapterId={}, hasContent={}",
                req.getChapterId(), matchedValue != null);

        return response;
    }

    @Override
    public NarrationCountRes getNarrationCount(NarrationCountReq req) {
        String sessionId = req.getSessionId();

        log.info("开始计算会话统计数量: sessionId={}", sessionId);

        // 参数校验
        if (StringUtils.isBlank(sessionId)) {
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), "会话ID不能为空");
        }

        // 1. 构建Redis缓存key（区分旁白统计和角色统计）
        String cacheKeySuffix = StringUtils.isBlank(characterId) ? "narration" : "character:" + characterId;
        String redisKey = RedisKeyConstant.getKey(RedisKeyConstant.NarrationCount.SESSION_NARRATION_COUNT + ":" + cacheKeySuffix, sessionId);
        RBucket<NarrationCountRes> bucket = redissonClient.getBucket(redisKey);
        NarrationCountRes cachedResult = bucket.get();

        if (cachedResult != null) {
            log.info("从Redis缓存获取统计数据: sessionId={}, characterId={}", sessionId, characterId);
            return cachedResult;
        }

        // 2. 查询会话下所有分镜数据
        List<AiShotPo> shotList = aiShotMapper.selectList(
                new LambdaQueryWrapper<AiShotPo>()
                        .eq(AiShotPo::getSessionId, sessionId)
                        .eq(AiShotPo::getDelFlag, 0)
                        .orderBy(true, true, AiShotPo::getSegmentId, AiShotPo::getQueue)
        );

        if (CollUtil.isEmpty(shotList)) {
            log.warn("未找到会话下的分镜数据: sessionId={}", sessionId);
            NarrationCountRes emptyResult = new NarrationCountRes();
            emptyResult.setSessionId(sessionId);
            emptyResult.setCharacterId(characterId);
            emptyResult.setCountType(StringUtils.isBlank(characterId) ? "narration" : "character");
            emptyResult.setChapters(new ArrayList<>());
            emptyResult.setTotalCount(0);
            return emptyResult;
        }

        // 3. 查询章节信息用于获取章节名称
        List<AiChapterPo> chapterList = aiChapterMapper.selectList(
                new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, sessionId)
                        .eq(AiChapterPo::getDelFlag, 0)
        );

        Map<String, String> chapterNameMap = chapterList.stream()
                .collect(Collectors.toMap(AiChapterPo::getSegmentId, AiChapterPo::getSegmentName));

        // 4. 按章节分组统计旁白
        Map<String, List<AiShotPo>> chapterShotMap = shotList.stream()
                .collect(Collectors.groupingBy(AiShotPo::getSegmentId));

        List<NarrationCountRes.ChapterNarrationInfo> chapterInfoList = new ArrayList<>();
        int totalCount = 0;
        boolean isCharacterCount = StringUtils.isNotBlank(characterId);

        for (Map.Entry<String, List<AiShotPo>> entry : chapterShotMap.entrySet()) {
            String segmentId = entry.getKey();
            List<AiShotPo> chapterShots = entry.getValue();

            NarrationCountRes.ChapterNarrationInfo chapterInfo = new NarrationCountRes.ChapterNarrationInfo();
            chapterInfo.setSegmentId(segmentId);
            chapterInfo.setSegmentName(chapterNameMap.getOrDefault(segmentId, "未知章节"));

            List<NarrationCountRes.NarrationDetail> details = new ArrayList<>();
            int chapterCount = 0;

            // 5. 遍历章节下的分镜，解析shot_data统计数据
            for (AiShotPo shot : chapterShots) {
                try {
                    if (StringUtils.isNotBlank(shot.getShotData())) {
                        // 解析shot_data JSON
                        ShotSaveReq.ShotGroupsDTO.ShotsDTO shotData = JSON.parseObject(shot.getShotData(), ShotSaveReq.ShotGroupsDTO.ShotsDTO.class);

                        if (shotData != null && CollUtil.isNotEmpty(shotData.getLineList())) {
                            // 遍历line_list
                            for (ShotSaveReq.ShotLines line : shotData.getLineList()) {
                                boolean shouldCount = false;

                                if (isCharacterCount) {
                                    // 角色统计：匹配charID
                                    shouldCount = characterId.equals(line.getCharID());
                                } else {
                                    // 旁白统计：匹配name为"旁白"
                                    shouldCount = "旁白".equals(line.getName());
                                }

                                if (shouldCount) {
                                    NarrationCountRes.NarrationDetail detail = new NarrationCountRes.NarrationDetail();
                                    detail.setShotId(shot.getShotId());
                                    detail.setId(line.getId());
                                    detail.setContent(line.getLine());

                                    if (isCharacterCount) {
                                        detail.setCharacterId(line.getCharID());
                                        detail.setCharacterName(line.getName());
                                    }

                                    details.add(detail);
                                    chapterCount++;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("解析分镜数据失败: shotId={}, shotData={}, error={}",
                            shot.getShotId(), shot.getShotData(), e.getMessage());
                    // 继续处理其他分镜，不中断整个流程
                }
            }

            chapterInfo.setCount(chapterCount);
            chapterInfo.setDetails(details);
            chapterInfoList.add(chapterInfo);
            totalCount += chapterCount;
        }

        // 6. 构建响应结果
        NarrationCountRes result = new NarrationCountRes();
        result.setSessionId(sessionId);
        result.setCharacterId(characterId);
        result.setCountType(isCharacterCount ? "character" : "narration");
        result.setChapters(chapterInfoList);
        result.setTotalCount(totalCount);
        // 7. 将结果保存到Redis缓存
        try {
            bucket.set(result, RedisKeyConstant.NarrationCount.NARRATION_COUNT_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("统计数据已保存到Redis: sessionId={}, characterId={}, totalCount={}",
                    sessionId, characterId, totalCount);
        } catch (Exception e) {
            log.error("保存统计数据到Redis失败: sessionId={}, characterId={}, error={}",
                    sessionId, characterId, e.getMessage());
            // 不影响主流程，继续返回结果
        }
        log.info("计算会话统计数量完成: sessionId={}, characterId={}, totalCount={}, chapterCount={}",
                sessionId, characterId, totalCount, chapterInfoList.size());
        return result;
    }

    @Override
    public UpdateNarrationAudioRes updateNarrationAudio(UpdateNarrationAudioReq req) {
        log.info("开始修改旁白音频: sessionId={}, segmentIds={}", req.getSessionId(), req.getSegmentIds());

        // 参数校验
        if (StringUtils.isBlank(req.getSessionId())) {
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), "会话ID不能为空");
        }

        // 1. 从Redis获取旁白统计数据
        String redisKey = RedisKeyConstant.getKey(RedisKeyConstant.NarrationCount.SESSION_NARRATION_COUNT, req.getSessionId());
        RBucket<NarrationCountRes> bucket = redissonClient.getBucket(redisKey);
        NarrationCountRes narrationData = bucket.get();

        if (narrationData == null) {
            log.warn("Redis中未找到旁白统计数据，尝试重新计算: sessionId={}", req.getSessionId());
            // 如果Redis中没有数据，先计算一次（只计算旁白，不计算角色）
            NarrationCountReq countReq = new NarrationCountReq();
            countReq.setSessionId(req.getSessionId());
            narrationData = getNarrationCount(countReq);
        }

        if (narrationData == null || CollUtil.isEmpty(narrationData.getChapters())) {
            log.warn("未找到旁白数据: sessionId={}", req.getSessionId());
            UpdateNarrationAudioRes emptyResult = new UpdateNarrationAudioRes();
            emptyResult.setSessionId(req.getSessionId());
            emptyResult.setProcessedChapterCount(0);
            emptyResult.setSuccessCount(0);
            emptyResult.setFailureCount(0);
            emptyResult.setProcessDetails(new ArrayList<>());
            return emptyResult;
        }

        // 2. 根据章节ID过滤数据
        List<NarrationCountRes.ChapterNarrationInfo> targetChapters;
        if (CollUtil.isEmpty(req.getSegmentIds())) {
            // 如果没有指定章节ID，处理所有章节
            targetChapters = narrationData.getChapters();
            log.info("未指定章节ID，将处理所有章节: count={}", targetChapters.size());
        } else {
            // 过滤指定的章节
            targetChapters = narrationData.getChapters().stream()
                    .filter(chapter -> req.getSegmentIds().contains(chapter.getSegmentId()))
                    .collect(Collectors.toList());
            log.info("指定章节ID过滤后的章节数量: {}", targetChapters.size());
        }

        if (CollUtil.isEmpty(targetChapters)) {
            log.warn("过滤后没有找到匹配的章节: sessionId={}, segmentIds={}", req.getSessionId(), req.getSegmentIds());
            UpdateNarrationAudioRes emptyResult = new UpdateNarrationAudioRes();
            emptyResult.setSessionId(req.getSessionId());
            emptyResult.setProcessedChapterCount(0);
            emptyResult.setSuccessCount(0);
            emptyResult.setFailureCount(0);
            emptyResult.setProcessDetails(new ArrayList<>());
            return emptyResult;
        }

        // 3. 获取会话音色配置
        Long soundId = req.getSoundId();
        if (soundId == null) {
            // 如果没有指定音色，使用会话默认音色
            AiCreationSessionPo sessionPo = aiCreationSessionMapper.selectOne(
                    new LambdaQueryWrapper<AiCreationSessionPo>()
                            .eq(AiCreationSessionPo::getSessionId, req.getSessionId())
            );
            if (sessionPo != null && sessionPo.getSoundId() != null) {
                soundId = sessionPo.getSoundId();
                log.info("使用会话默认音色: sessionId={}, soundId={}", req.getSessionId(), soundId);
            } else {
                log.warn("未找到会话信息或会话未配置音色: sessionId={}", req.getSessionId());
                throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), "未找到音色配置，请指定音色ID或配置会话默认音色");
            }
        }

        // 4. 验证音色是否存在
        AgentSoundPo soundPo = agentSoundMapper.selectOne(
                new LambdaQueryWrapper<AgentSoundPo>()
                        .eq(AgentSoundPo::getId, soundId)
                        .eq(AgentSoundPo::getDelFlag, 0)
        );
        if (soundPo == null) {
            log.warn("音色不存在: soundId={}", soundId);
            throw new BizException(ErrorCodeEnum.PARAM_ERROR.getCode(), "音色不存在或已被删除");
        }
        log.info("验证音色成功: soundId={}, soundName={}", soundId, soundPo.getUserName());

        // 5. 收集所有需要处理的旁白
        List<NarrationTask> narrationTasks = new ArrayList<>();
        for (NarrationCountRes.ChapterNarrationInfo chapter : targetChapters) {
            if (CollUtil.isEmpty(chapter.getDetails())) {
                continue;
            }

            for (NarrationCountRes.NarrationDetail narration : chapter.getDetails()) {
                NarrationTask task = new NarrationTask();
                task.setChapter(chapter);
                task.setNarration(narration);
                task.setSoundId(soundId);
                task.setSessionId(req.getSessionId());
                narrationTasks.add(task);
            }
        }

        if (narrationTasks.isEmpty()) {
            log.info("没有需要处理的旁白任务: sessionId={}", req.getSessionId());
            UpdateNarrationAudioRes emptyResult = new UpdateNarrationAudioRes();
            emptyResult.setSessionId(req.getSessionId());
            emptyResult.setProcessedChapterCount(targetChapters.size());
            emptyResult.setSuccessCount(0);
            emptyResult.setFailureCount(0);
            emptyResult.setProcessDetails(new ArrayList<>());
            return emptyResult;
        }

        // 6. 并发处理旁白音频生成（最多5个并发）
        log.info("开始并发处理旁白音频: sessionId={}, totalTasks={}", req.getSessionId(), narrationTasks.size());

        Semaphore semaphore = new Semaphore(5); // 最多5个并发
        List<CompletableFuture<UpdateNarrationAudioRes.ProcessDetail>> futures = new ArrayList<>();

        for (NarrationTask task : narrationTasks) {
            CompletableFuture<UpdateNarrationAudioRes.ProcessDetail> future = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            semaphore.acquire(); // 获取信号量
                            return processNarrationTask(task);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            return createFailedDetail(task, "任务被中断: " + e.getMessage());
                        } finally {
                            semaphore.release(); // 释放信号量
                        }
                    }, taskExecutor);
            futures.add(future);
        }

        // 7. 等待所有任务完成
        List<UpdateNarrationAudioRes.ProcessDetail> processDetails = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (CompletableFuture<UpdateNarrationAudioRes.ProcessDetail> future : futures) {
            try {
                UpdateNarrationAudioRes.ProcessDetail detail = future.get();
                processDetails.add(detail);

                if ("SUCCESS".equals(detail.getStatus())) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("获取并发任务结果失败", e);
                failureCount++;
            }
        }

        // 6. 构建响应结果
        UpdateNarrationAudioRes result = new UpdateNarrationAudioRes();
        result.setSessionId(req.getSessionId());
        result.setProcessedChapterCount(targetChapters.size());
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setProcessDetails(processDetails);

        log.info("修改旁白音频完成: sessionId={}, processedChapters={}, success={}, failure={}",
                req.getSessionId(), targetChapters.size(), successCount, failureCount);

        return result;
    }

    /**
     * 处理单个旁白任务
     */
    private UpdateNarrationAudioRes.ProcessDetail processNarrationTask(NarrationTask task) {
        UpdateNarrationAudioRes.ProcessDetail detail = new UpdateNarrationAudioRes.ProcessDetail();
        detail.setSegmentId(task.getChapter().getSegmentId());
        detail.setSegmentName(task.getChapter().getSegmentName());
        detail.setShotId(task.getNarration().getShotId());
        detail.setNarrationId(task.getNarration().getId());
        detail.setNarrationLine(task.getNarration().getContent());

        try {
            // 调用TTS生成接口
            TtsGenerateReq ttsReq = new TtsGenerateReq();
            ttsReq.setConversationId(task.getSessionId());
            ttsReq.setContentId(task.getNarration().getShotId());
            ttsReq.setIndex(task.getNarration().getId());
            ttsReq.setText(task.getNarration().getContent());
            ttsReq.setVoiceId(String.valueOf(task.getSoundId()));
            ttsReq.setSoundType("narration");
            ttsReq.setSource(2); // 来源：画布

            log.info("调用TTS生成音频: shotId={}, narrationId={}, text={}",
                    task.getNarration().getShotId(), task.getNarration().getId(),
                    task.getNarration().getContent());

            TtsGenerateRes ttsRes = aiCreationContentService.generateTts(ttsReq);

            if (ttsRes != null && StringUtils.isNotBlank(ttsRes.getAudioUrl())) {
                detail.setStatus("SUCCESS");
                detail.setAudioUrl(ttsRes.getAudioUrl());
                detail.setDuration(ttsRes.getDuration());
                detail.setRecordId(ttsRes.getRecordId());
                log.info("TTS生成成功: shotId={}, narrationId={}, audioUrl={}",
                        task.getNarration().getShotId(), task.getNarration().getId(),
                        ttsRes.getAudioUrl());
            } else {
                detail.setStatus("FAILED");
                detail.setErrorMessage("TTS生成失败，返回结果为空");
                log.error("TTS生成失败: shotId={}, narrationId={}, 返回结果为空",
                        task.getNarration().getShotId(), task.getNarration().getId());
            }

        } catch (Exception e) {
            detail.setStatus("FAILED");
            detail.setErrorMessage(e.getMessage());
            log.error("TTS生成异常: shotId={}, narrationId={}, error={}",
                    task.getNarration().getShotId(), task.getNarration().getId(),
                    e.getMessage(), e);
        }

        return detail;
    }

    /**
     * 创建失败的处理详情
     */
    private UpdateNarrationAudioRes.ProcessDetail createFailedDetail(NarrationTask task, String errorMessage) {
        UpdateNarrationAudioRes.ProcessDetail detail = new UpdateNarrationAudioRes.ProcessDetail();
        detail.setSegmentId(task.getChapter().getSegmentId());
        detail.setSegmentName(task.getChapter().getSegmentName());
        detail.setShotId(task.getNarration().getShotId());
        detail.setNarrationId(task.getNarration().getId());
        detail.setNarrationLine(task.getNarration().getContent());
        detail.setStatus("FAILED");
        detail.setErrorMessage(errorMessage);
        return detail;
    }

    /**
     * 旁白任务内部类
     */
    private static class NarrationTask {
        private NarrationCountRes.ChapterNarrationInfo chapter;
        private NarrationCountRes.NarrationDetail narration;
        private Long soundId;
        private String sessionId;

        // Getters and Setters
        public NarrationCountRes.ChapterNarrationInfo getChapter() { return chapter; }
        public void setChapter(NarrationCountRes.ChapterNarrationInfo chapter) { this.chapter = chapter; }

        public NarrationCountRes.NarrationDetail getNarration() { return narration; }
        public void setNarration(NarrationCountRes.NarrationDetail narration) { this.narration = narration; }

        public Long getSoundId() { return soundId; }
        public void setSoundId(Long soundId) { this.soundId = soundId; }

        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    }
}