package com.wlink.agent.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 旁白统计响应
 */
@Data
@Schema(description = "旁白统计响应")
public class NarrationCountRes {

    @Schema(description = "会话ID")
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "角色ID，如果为空则统计旁白")
    @JsonProperty("characterId")
    private String characterId;

    @Schema(description = "统计类型：narration-旁白统计，character-角色统计")
    @JsonProperty("countType")
    private String countType;

    @Schema(description = "章节统计列表")
    @JsonProperty("chapters")
    private List<ChapterNarrationInfo> chapters;

    @Schema(description = "总数量")
    @JsonProperty("totalCount")
    private Integer totalCount;

    /**
     * 章节旁白信息
     */
    @Data
    @Schema(description = "章节旁白信息")
    public static class ChapterNarrationInfo {

        @Schema(description = "章节ID")
        @JsonProperty("segmentId")
        private String segmentId;

        @Schema(description = "章节名称")
        @JsonProperty("segmentName")
        private String segmentName;

        @Schema(description = "旁白数量")
        @JsonProperty("narrationCount")
        private Integer narrationCount;

        @Schema(description = "旁白详情列表")
        @JsonProperty("narrationDetails")
        private List<NarrationDetail> narrationDetails;
    }

    /**
     * 旁白详情
     */
    @Data
    @Schema(description = "旁白详情")
    public static class NarrationDetail {

        @Schema(description = "分镜ID")
        @JsonProperty("shotId")
        private String shotId;

        @Schema(description = "旁白ID")
        @JsonProperty("narrationId")
        private Integer narrationId;

        @Schema(description = "旁白内容")
        @JsonProperty("narrationLine")
        private String narrationLine;
    }
}
