package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 旁白统计请求
 */
@Data
@Schema(description = "旁白统计请求")
public class NarrationCountReq {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true)
    private String sessionId;

    @Schema(description = "角色ID，如果不传则统计旁白数量，如果传了则统计该角色的音频数量")
    private String characterId;
}
