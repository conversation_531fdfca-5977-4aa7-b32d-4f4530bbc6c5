package com.wlink.agent.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 修改旁白音频请求
 */
@Data
@Schema(description = "修改旁白音频请求")
public class UpdateNarrationAudioReq {

    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true)
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "章节ID数组，如果为空则处理所有章节")
    @JsonProperty("segmentIds")
    private List<String> segmentIds;

    @Schema(description = "音色ID，如果不传则使用会话默认音色")
    @JsonProperty("voiceId")
    private String voiceId;

    @Schema(description = "语速，范围[0.5,2]，默认值为1.0")
    @JsonProperty("rate")
    private String rate;

    @Schema(description = "音调，范围[-12,12]，默认值为0")
    @JsonProperty("pitch")
    private Integer pitch;

    @Schema(description = "音量，范围[0,10]，默认值为1.0")
    @JsonProperty("volume")
    private String volume;

    @Schema(description = "情绪，可选值：happy, sad, angry, fearful, disgusted, surprised, neutral")
    @JsonProperty("emotion")
    private String emotion;

    @Schema(description = "音调列表")
    @JsonProperty("tone")
    private List<String> tone;
}
