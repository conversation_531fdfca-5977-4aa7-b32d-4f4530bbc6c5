package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wlink.agent.annotation.LogRequest;
import com.wlink.agent.annotation.IgnoreRequestUser;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.model.req.ChapterDesignContentQueryReq;
import com.wlink.agent.model.req.ShotUpdateReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.UpdateNarrationAudioReq;
import com.wlink.agent.model.res.ChapterDesignContentRes;
import com.wlink.agent.model.res.ChapterRes;
import com.wlink.agent.model.res.NarrationCountRes;
import com.wlink.agent.model.res.ShotRes;
import com.wlink.agent.model.res.UpdateNarrationAudioRes;
import com.wlink.agent.service.AiCreationContentService;
import com.wlink.agent.service.AiShotService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分镜控制
 * 提供查询章节列表、分镜列表和修改分镜数据的接
 */
@Slf4j
@RestController
@RequestMapping("/agent/ai-shot")
@Tag(name = "分镜控制")
@RequiredArgsConstructor
public class AiShotController {

    private final AiShotService aiShotService;

    /**
     * 根据会话ID查询章节列表
     *
     * @param conversationId 会话ID
     * @return 章节列表
     */
    @GetMapping("/chapters/{conversationId}")
    @Operation(summary = "查询章节列表", description = "根据会话ID查询章节列表")
    public MultiResponse<ChapterRes> listChapters(
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId) {
        log.info("查询章节列表: conversationId={}", conversationId);
        List<ChapterRes> chapters = aiShotService.listChapters(conversationId);
        return MultiResponse.of(chapters);
    }

    /**
     * 根据章节ID查询分镜列表
     *
     * @param conversationId 会话ID
     * @param segmentId 章节ID
     * @return 分镜列表
     */
    @GetMapping("/shots/{conversationId}/{segmentId}")
    @Operation(summary = "查询分镜列表", description = "根据会话ID和章节ID查询分镜列表")
    public MultiResponse<ShotRes> listShots(
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
            @Parameter(description = "章节ID", required = true) @PathVariable String segmentId) {
        
        log.info("查询分镜列表: conversationId={}, segmentId={}", conversationId, segmentId);
        List<ShotRes> shots = aiShotService.listShots(conversationId, segmentId);
        return MultiResponse.of(shots);
    }

    /**
     * 根据分镜ID修改分镜数据
     *
     * @param req 分镜修改请求
     * @return 操作结果
     */
    @PostMapping("/update")
    @Operation(summary = "修改分镜数据", description = "根据分镜ID修改分镜数据")
    public SingleResponse<ShotRes> updateShot(@Valid @RequestBody ShotUpdateReq req) {
        log.info("修改分镜数据: shotId={}", req.getShotId());
        return SingleResponse.of(aiShotService.updateShot(req));
    }

    /**
     * 根据章节ID查询会话设计内容
     *
     * @param chapterId 章节ID
     * @return 设计内容
     */
    @GetMapping("/design-content/{chapterId}")
    @Operation(summary = "查询章节设计内容", description = "根据章节ID查询会话设计内容")
    public SingleResponse<ChapterDesignContentRes> getChapterDesignContent(
            @Parameter(description = "章节ID", required = true) @PathVariable Long chapterId) {
        log.info("查询章节设计内容: chapterId={}", chapterId);

        ChapterDesignContentQueryReq req = new ChapterDesignContentQueryReq();
        req.setChapterId(chapterId);

        ChapterDesignContentRes result = aiShotService.getChapterDesignContent(req);
        return SingleResponse.of(result);
    }

    /**
     * 计算会话下所有章节的旁白数量
     *
     * @param sessionId 会话ID
     * @return 旁白统计结果
     */
    @GetMapping("/narration-count/{sessionId}")
    @Operation(summary = "计算会话旁白数量", description = "计算指定会话下所有章节的旁白数量统计")
    public SingleResponse<NarrationCountRes> getNarrationCount(
            @Parameter(description = "会话ID", required = true) @PathVariable String sessionId) {
        log.info("计算会话旁白数量: sessionId={}", sessionId);

        NarrationCountRes result = aiShotService.getNarrationCountBySession(sessionId);
        return SingleResponse.of(result);
    }

    /**
     * 修改旁白音频
     *
     * @param req 修改旁白音频请求
     * @return 修改结果
     */
    @PostMapping("/narration-audio/update")
    @Operation(summary = "修改旁白音频", description = "根据会话ID和章节ID批量生成旁白音频")
    public SingleResponse<UpdateNarrationAudioRes> updateNarrationAudio(
            @Parameter(description = "修改旁白音频请求", required = true) @Valid @RequestBody UpdateNarrationAudioReq req) {
        log.info("修改旁白音频: sessionId={}, segmentIds={}", req.getSessionId(), req.getSegmentIds());

        UpdateNarrationAudioRes result = aiShotService.updateNarrationAudio(req);
        return SingleResponse.of(result);
    }

}
