package com.wlink.agent.enums;

import lombok.Getter;

/**
 * 积分交易类型枚举
 */
@Getter
public enum TransactionTypeEnum {
    
    /**
     * 视频生成消耗
     */
    VIDEO_GENERATION(5, "视频生成消耗"),
    
    /**
     * 视频生成失败退款
     */
    VIDEO_REFUND(6, "视频生成失败退款"),
    
    /**
     * 支付获得积分
     */
    PAYMENT_REWARD(7, "支付获得积分"),
    
    /**
     * 退款扣除积分
     */
    REFUND_DEDUCT(8, "退款扣除积分"),
    
    /**
     * 声音复刻消耗
     */
    VOICE_CLONE(9, "声音复刻消耗"),
    
    /**
     * 声音复刻失败退款
     */
    VOICE_CLONE_REFUND(10, "声音复刻失败退款");
    
    /**
     * 交易类型编码
     */
    private final int code;
    
    /**
     * 交易类型描述
     */
    private final String desc;
    
    TransactionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
} 