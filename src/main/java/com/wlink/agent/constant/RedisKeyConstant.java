package com.wlink.agent.constant;

/**
 * Redis Key常量类
 */
public class RedisKeyConstant {

    /**
     * 直播间相关key前缀
     */
    public static class LiveRoom {
        /**
         * 直播间信息 hash结构
         * key: LIVE_ROOM:{roomId}
         * field: 
         *   - userCount: 在线用户数
         *   - status: 直播状态
         */
        public static final String ROOM_INFO = "LIVE_ROOM:%s";

        /**
         * 直播间用户集合 set结构
         * key: LIVE_ROOM:USERS:{roomId}
         * member: userId
         */
        public static final String ROOM_USERS = "LIVE_ROOM:USERS:%s";

        /**
         * 用户-房间关系 string结构
         * key: LIVE_ROOM:USER_ROOM:{userId}
         * value: roomId
         */
        public static final String USER_ROOMS = "LIVE_ROOM:USER_ROOMS:%s";

        /**
         * 直播间广播消息队列 list结构
         * key: LIVE_ROOM:ROOM_BROADCAST:{roomId}
         */
        public static final String ROOM_BROADCAST = "LIVE_ROOM:ROOM_BROADCAST:";


        /**
         * 直播间消息队列 list结构
         * key: LIVE_ROOM:MSG_QUEUE:{roomId}
         */
        public static final String DANMAKU_MSG_QUEUE = "LIVE_ROOM:DANMAKU_MSG_QUEUE:%s";

        //直播间礼物消息队列
        public static final String ROOM_GIFT_MSG_QUEUE = "LIVE_ROOM:ROOM_GIFT_MSG_QUEUE:%s";

        /**
         * 直播间播报消息
         */
        public static final String BROADCAST_MESSAGE = "LIVE_ROOM:BROADCAST_MESSAGE:%s";

        //直播间弹幕回复会话idkey
        public static final String DANMAKU_REPLY_CONVERSATION_ID = "LIVE_ROOM:DANMAKU_REPLY_CONVERSATION_ID:%s";

        //runDown生成会话id Key
        public static final String RUN_DOWN_CONVERSATION_ID = "LIVE_ROOM:RUN_DOWN_CONVERSATION_ID:%s";


        /**
         * 直播间弹幕消息回复标识 String结构
         *
         */
        public static final String DANMAKU_REPLY_FLAG = "LIVE_ROOM:DANMAKU_REPLY_FLAG:%s";

        /**
         * 直播间回复延迟队列 list结构
         */
        public static final String DANMAKU_REPLY_QUEUE = "LIVE_ROOM:DANMAKU_REPLY_QUEUE";

        /**
         * 直播间第一次弹幕标识 String结构
         *
         */
        public static final String ROOM_FIRST_DANMAKU_FLAG = "LIVE_ROOM:FIRST_DANMAKU_FLAG:%s";


        /**
         * 多人直播间弹幕回复缓存队列
         */
        public static final String MULTI_LIVE_STREAM_REPLY_QUEUE = "LIVE_ROOM:MULTI_LIVE_STREAM_REPLY_QUEUE:%s";

        //多人直播弹幕回复结束标识
        public static final String MULTI_LIVE_STREAM_REPLY_END_FLAG = "LIVE_ROOM:MULTI_LIVE_STREAM_REPLY_END_FLAG:%s";
    }

    /**
     * 智能体相关key前缀
     */
    public static class Agent {
        /**
         * 智能体信息 hash结构
         * key: AGENT:INFO:{agentId}
         */
        public static final String AGENT_INFO = "AGENT:INFO:%s";

        /**
         * 智能体直播状态 string结构
         * key: AGENT:LIVE_STATUS:{agentId}
         */
        public static final String LIVE_STATUS = "AGENT:LIVE_STATUS:%s";

        /**
         * 智能体对话历史 list结构
         * key: AGENT:CHAT_HISTORY:{agentId}
         */
        public static final String CHAT_HISTORY = "AGENT:CHAT_HISTORY:%s";
    }

    /**
     * 形象生成相关key前缀
     */
    public static class ImageGeneration {
        /**
         * 形象生成记录 hash结构
         * key: IMAGE:GENERATION:{recordId}
         */
        public static final String GENERATION_RECORD = "IMAGE:GENERATION:%s";

        /**
         * 形象生成任务队列 list结构
         * key: IMAGE:TASK_QUEUE
         */
        public static final String TASK_QUEUE = "IMAGE:TASK_QUEUE";

        /**
         * 形象生成锁 string结构
         * key: IMAGE:LOCK:{userId}
         */
        public static final String GENERATION_LOCK = "IMAGE:LOCK:%s";
    }

    /**
     * 渲染
     */
    public static class Rendering {
        public static final String AGENT_RENDERING = "AGENT:RENDERING";
    }

    /**
     * 聊天相关key前缀
     */
    public static class Chat {
        /**
         * Dify会话ID前缀
         * key: DIFY_CONVERSATION_{sessionId}
         */
        public static final String DIFY_CONVERSATION = "DIFY_CONVERSATION_";
        
        /**
         * 高token使用标识
         * key: CHAT:HIGH_TOKEN_FLAG:{sessionId}
         */
        public static final String HIGH_TOKEN_FLAG = "CHAT:HIGH_TOKEN_FLAG:%s";
    }
    
    /**
     * 内容相关key前缀
     */
    public static class Content {
        /**
         * 用户标签数据 hash结构
         * key: CONTENT:USER_TAGS:{userId}
         */
        public static final String USER_TAGS = "CONTENT:USER_TAGS:%s";
        
        /**
         * 用户标签数据最后更新时间 string结构
         * key: CONTENT:USER_TAGS_LAST_UPDATE:{userId}
         */
        public static final String USER_TAGS_LAST_UPDATE = "CONTENT:USER_TAGS_LAST_UPDATE:%s";
        
        /**
         * 标签数据过期时间（秒）
         */
        public static final long TAGS_EXPIRE_SECONDS = 3600 * 24 * 30; // 24小时
        
        /**
         * 标签数据最小更新间隔（毫秒）
         */
        public static final long TAGS_MIN_UPDATE_INTERVAL = 5 * 60 * 1000; // 5分钟
    }

    /**
     * 用户活跃度相关key前缀
     */
    public static class UserActivity {
        /**
         * 每日活跃用户集合 set结构
         * key: USER:ACTIVE:{date}
         * member: userId
         */
        public static final String DAILY_ACTIVE_USERS = "USER:ACTIVE:%s";
        
        /**
         * 活跃用户数据过期时间（秒）
         */
        public static final long ACTIVE_USERS_EXPIRE_SECONDS = 3600 * 24 * 1; // 30天
    }

    /**
     * 微信JS-SDK相关缓存键
     */
    public static final String WECHAT_ACCESS_TOKEN = "wechat:access_token";
    public static final String WECHAT_JSAPI_TICKET = "wechat:jsapi_ticket";

    /**
     * 获取完整的Redis key
     */
    public static String getKey(String keyPattern, Object... params) {
        return String.format(keyPattern, params);
    }
} 